import { RiTimeLine } from "@remixicon/react";
import { IMAGE_PATH } from "globals";
import { useSidebarLinks } from "hooks";
import { Image } from "react-bootstrap";
import { Link, useLocation } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import "./styles.scss";

const Sidebar = () => {
  const location = useLocation();
  const userData = useUserStore((state) => state.userInfo.user);
  const [sidebarLinks] = useSidebarLinks();

  const checkActivePath = (link: any) => {
    const pathsToCheck = [link.path];

    if (link.subItems && Array.isArray(link.subItems)) {
      link.subItems.forEach((sub: any) => pathsToCheck.push(sub.path));
    }

    return pathsToCheck.includes(location.pathname);
  };

  return (
    <aside className="sidebar bg-blue h-100 d-flex flex-column">
      <Link
        to={
          userData?.is_subscription ? ROUTE_PATH.HOME : ROUTE_PATH.SUBSCRIPTIONS
        }
        className="d-block sidebar-logo"
      >
        <Image
          src={IMAGE_PATH.singleLogo}
          alt="logo"
          className="object-fit-contain w-100 h-100"
        />
      </Link>

      <hr className="p-0 opacity-100 m-0" />

      <ul className="list-unstyled m-0 p-0 sidebar-navigation d-flex flex-column">
        <li className="text-center sidebar-navigation-item">
          {sidebarLinks.map((link: any) => (
            <Link
              key={link.path}
              to={link.path}
              className={`text-decoration-none d-block position-relative d-flex flex-column align-items-center justify-content-center ${
                checkActivePath(link) ? "item-active" : ""
              }`}
            >
              <link.icon size={"24px"} />
              <span className="d-block lh-sm">
                <span>{link.label}</span>
                {link?.coming_soon && (
                  <small className="badge-coming-soon">
                    <RiTimeLine size={18} /> Coming Soon
                  </small>
                )}
              </span>
            </Link>
          ))}
        </li>
      </ul>

      <div className="sidebar-footer-img mt-auto position-relative">
        <Image
          src={IMAGE_PATH.sidebarFooterImage}
          alt="footer"
          className="object-fit-contain w-100 h-100"
        />
        {/* <Image src={IMAGE_PATH.sidebarFooterImageText} alt="" className="object-fit-contain w-100 h-100" /> */}
        <p className="mb-0 pt-2 font-light text-center position-absolute top-50 start-50 translate-middle w-100">
          Secure & Trusted
        </p>
      </div>
    </aside>
  );
};

export default Sidebar;
