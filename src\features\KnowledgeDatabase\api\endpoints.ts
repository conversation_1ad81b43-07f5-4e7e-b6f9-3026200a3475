import { IDType } from "types";

const createUrl = (...arg: string[]) => {
  return arg.map((a) => a.replace(/\/$/, "")).join("/");
};

export const API_ENDPOINTS = {
  GET_KNOWLEDGE_BASE: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/knowledgebase`
  ),
  ADD_KNOWLEDGE_BASE: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/knowledgebase`
  ),
  DELETE_FILE: (id: IDType) =>
    createUrl(
      import.meta.env.VITE_CHAT_API_URL,
      `chat_ai/knowledgebase/file/${id}`
    ),
  DELETE_FOLDER: (id: IDType) =>
    createUrl(
      import.meta.env.VITE_CHAT_API_URL,
      `chat_ai/knowledgebase/folder/${id}`
    ),
  UPDATE_PERMISSION_GROUP: (id: IDType) =>
    createUrl(
      import.meta.env.VITE_CHAT_API_URL,
      `chat_ai/knowledgebase/${id}/permissions`
    ),
  GET_FAQ_PROMPTS: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/faq_prompts`
  ),
};
