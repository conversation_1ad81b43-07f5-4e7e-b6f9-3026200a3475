import { RiCloseLine } from "@remixicon/react";
import { CustomDropdown } from "components";
import { useFormik } from "formik";
import { DEFAULT_PROMPT_ICONS } from "globals";
import React, { useState } from "react";
import { Button, Dropdown, DropdownButton, Form, Modal } from "react-bootstrap";
import * as Yup from "yup";
import { useGetKnowledgeBase } from "../api";
import { OWNER_TYPES } from "../globals";

interface FAQPromptModalProps {
  show: boolean;
  onClose: () => void;
  onSave?: (data: any) => void;
}

const promptContextItems = [
  {
    value: "organisation",
    label: "Organisational",
  },
  {
    value: "files",
    label: "Specific Folder/File",
  },
];

// Validation schema for FAQ Prompt
const FAQPromptValidations = Yup.object().shape({
  title: Yup.string()
    .max(40, "Title must be at most 40 characters")
    .required("Prompt title is required"),
  promptContext: Yup.string().required("Prompt context is required"),
  subContext: Yup.string().when("promptContext", {
    is: "folderorfile",
    then: (schema) =>
      schema.required(
        "Sub context is required when Specific Folder/File is selected"
      ),
    otherwise: (schema) => schema.notRequired(),
  }),
  userGroup: Yup.string().required("User group is required"),
  icon: Yup.string().required("Emoticon is required"),
});

// User group options
const USER_GROUP_OPTIONS = [
  { value: "personal", label: "Personal" },
  { value: "admin", label: "Admin" },
  { value: "user", label: "User" },
];

const FAQPromptModal: React.FC<FAQPromptModalProps> = ({
  show,
  onClose,
  onSave,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Fetch knowledge base items for sub context dropdown
  const { data: { files = [] } = {} } = useGetKnowledgeBase({
    owner_type: OWNER_TYPES.ORGANIZATION,
    search: searchQuery,
  });

  const formik: any = useFormik({
    initialValues: {
      title: "",
      promptContext: "",
      subContext: "",
      userGroup: "",
      icon: "",
    },
    validationSchema: FAQPromptValidations,
    onSubmit: async (values) => {
      if (onSave) {
        onSave(values);
      }
      onClose();
    },
  });

  // Transform knowledge base files to dropdown items
  const knowledgeBaseItems = files.map((file: any) => ({
    label: file.name,
    value: JSON.stringify({ id: file.id, name: file.name, type: file.type }),
  }));

  const handleIconSelect = (icon: string) => {
    setSelectedIcon(DEFAULT_PROMPT_ICONS[icon]);
    formik.setFieldValue("icon", icon);
  };

  const handlePromptContextSelect = (value: string) => {
    formik.setFieldValue("promptContext", value);
    // Reset sub context when prompt context changes
    if (value !== "folderorfile") {
      formik.setFieldValue("subContext", "");
    }
  };

  const handleSubContextSelect = (value: string) => {
    formik.setFieldValue("subContext", value);
  };

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2 text-danger">{formik.errors[field]}</span>;
    }
  };

  const handleClose = () => {
    formik.resetForm();
    setSelectedIcon("");
    setSearchQuery("");
    onClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      keyboard={false}
      centered
      className="add-prompt-card-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Create FAQ Prompt
            </h1>
          </div>

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <div className="d-flex flex-lg-row flex-column gap-3">
                <Form.Group className="m-0 p-0 position-relative w-100">
                  <Form.Label>Prompt Title</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Max 40 characters"
                    {...formik.getFieldProps("title")}
                  />
                  <div className="d-flex flex-column">
                    <small className="font-gray position-static">
                      *Max 40 characters
                    </small>
                    {renderError("title")}
                  </div>
                </Form.Group>

                <Form.Group className="m-0 p-0 position-relative">
                  <Form.Label>Emoticon</Form.Label>
                  <DropdownButton
                    id="icon-dropdown"
                    title={selectedIcon || "Select"}
                    variant="outline-secondary"
                    className="icon-dropdown w-100"
                  >
                    {Object.keys(DEFAULT_PROMPT_ICONS).map((icon: any) => {
                      const EmotiIcon = DEFAULT_PROMPT_ICONS[icon];
                      return (
                        <Dropdown.Item
                          key={icon}
                          onClick={() => handleIconSelect(icon)}
                        >
                          <EmotiIcon size={24} />{" "}
                          <div className="text-capitalize d-inline-block">
                            {icon}
                          </div>
                        </Dropdown.Item>
                      );
                    })}
                  </DropdownButton>
                  {renderError("icon")}
                </Form.Group>
              </div>

              <Form.Group className="m-0 p-0 position-relative">
                <Form.Label>Prompt Context</Form.Label>
                <CustomDropdown
                  title="Select Prompt Context"
                  items={promptContextItems}
                  className="w-100"
                  onSelect={handlePromptContextSelect}
                  value={formik.values.promptContext}
                />
                {renderError("promptContext")}
              </Form.Group>

              {formik.values.promptContext === "files" && (
                <Form.Group className="m-0 p-0 position-relative">
                  <Form.Label>Sub Context</Form.Label>
                  <CustomDropdown
                    title="Select Knowledge Base Item"
                    items={knowledgeBaseItems}
                    className="w-100"
                    onSelect={handleSubContextSelect}
                    value={formik.values.subContext}
                    searchEnabled={true}
                    onSearch={setSearchQuery}
                  />
                  {renderError("subContext")}
                </Form.Group>
              )}

              <Form.Group className="m-0 p-0 position-relative">
                <Form.Label>Prompt User Group</Form.Label>
                <DropdownButton
                  id="user-group-dropdown"
                  title={
                    formik.values.userGroup
                      ? USER_GROUP_OPTIONS.find(
                          (option) => option.value === formik.values.userGroup
                        )?.label
                      : "Select User Group"
                  }
                  variant="outline-secondary"
                  className="w-100"
                >
                  {USER_GROUP_OPTIONS.map((option) => (
                    <Dropdown.Item
                      key={option.value}
                      onClick={() =>
                        formik.setFieldValue("userGroup", option.value)
                      }
                    >
                      {option.label}
                    </Dropdown.Item>
                  ))}
                </DropdownButton>
                {renderError("userGroup")}
              </Form.Group>

              <div
                className="action-btns mt-3 d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={!formik.isValid}
                >
                  Create
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default FAQPromptModal;
