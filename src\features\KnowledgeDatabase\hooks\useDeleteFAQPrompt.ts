import toast from "react-hot-toast";
import {
    setConfirmModalConfig
} from "stores";
import { IDType } from "types";
import { useDeleteFile, useDeleteFolder } from "../api";
import { useInvalidateQuery } from "hooks";

type DeleteType = 'folder' | 'file'

const useDeleteFileOrFolder = () => {
    const { mutateAsync: deleteFile } = useDeleteFile();
    const { mutateAsync: deleteFolder } = useDeleteFolder();
    const [invalidateQueries] = useInvalidateQuery();

    const deleteOperation = {
        file: (id: IDType) => deleteFile({ id }),
        folder: (id: IDType) => deleteFolder({ id }),
    }

    const handleDelete = async ({ type, itemId }: { type: DeleteType, itemId: IDType }) => {
        try {
            const result: any = await deleteOperation[type](itemId)
            if (result?.success) {
                toast.success(result?.message || `${type} Deleted Successfully!`);
                invalidateQueries(['knowledge-base'])
            }
        } catch (err: any) {
            console.log(err);
        }
    };

    const onClickDelete = ({ type, itemId }: { type: DeleteType, itemId: IDType }) => {
        setConfirmModalConfig({
            visible: true,
            data: {
                onSubmit: () => handleDelete({ type, itemId }),
                content: {
                    heading: `Delete ${type}`,
                    description: `Are you sure you want to delete this ${type}?`,
                },
            },
        });
    };

    return { onClickDelete };
};

export default useDeleteFileOrFolder;
