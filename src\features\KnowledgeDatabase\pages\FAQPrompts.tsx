import { Ri<PERSON>ore2Fill } from "@remixicon/react";
import { CustomDropdown, DataGridTable } from "components";
import { FAQPromptsToolbar } from "features/KnowledgeDatabase/components";
import { DEFAULT_PROMPT_ICONS } from "globals";
import { useDebounce } from "hooks";
import { useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { getFormattedDate } from "utils";
import { useGetFAQPrompts } from "../api";
import { OWNER_TYPES } from "../globals";
import useDeleteFileOrFolder from "../hooks/useDeleteFileOrFolder";
import "./styles.scss";

export interface FilterInterface {
  search?: string | undefined;
  owner_type?: string | undefined;
  sort?: string;
}

export default function FAQPrompts() {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });
  const [filters, setFilters] = useState<FilterInterface>({
    search: undefined,
    owner_type: OWNER_TYPES.ORGANIZATION,
    sort: "asc",
  });

  const debouncedQry = useDebounce(filters.search, 500);

  const { data: { prompts = [], count: totalCount } = {}, isLoading } =
    useGetFAQPrompts({
      ...paginationConfig,
      search: debouncedQry,
    });

  const { onClickDelete } = useDeleteFileOrFolder();

  const columns = [
    {
      field: "title",
      headerName: "Title",
      renderCell: (row: any) => {
        const Icon = row?.icon
          ? DEFAULT_PROMPT_ICONS[row.icon]
          : DEFAULT_PROMPT_ICONS.document;

        return (
          <div className="table-user-card d-flex justify-content-start align-items-center">
            <Icon />
            <div className="table-user-card-data">
              <h3 className="mb-0 name">{row?.title}</h3>
            </div>
          </div>
        );
      },
    },
    {
      field: "context",
      headerName: "Context",
      renderCell: (row: any) => (
        <p className="mb-0 text-capitalize">{row?.context ?? "NA"}</p>
      ),
    },
    {
      field: "sub_context",
      headerName: "Sub Context",
      renderCell: (row: any) => (
        <p className="mb-0">
          {row?.sub_context?.type
            ? `${row.sub_context.type} (${row.sub_context.ids?.join(", ")})`
            : "N/A"}
        </p>
      ),
    },
    {
      field: "access_control",
      headerName: "Allowed Roles",
      renderCell: (row: any) => (
        <p className="mb-0 text-capitalize">
          {row?.access_control?.allowed_roles?.join(", ") || "—"}
        </p>
      ),
    },
    {
      field: "created_at",
      headerName: "Created Date",
      renderCell: (row: any) => (
        <p className="mb-0">{getFormattedDate(row?.created_at) ?? "NA"}</p>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => {
        const onSelect = (value: string) => {
          if (value === "delete") {
            onClickDelete({ type: "prompt", itemId: row?.id });
          }
        };

        return (
          <div className="d-flex gap-2" onClick={(e) => e.stopPropagation()}>
            <CustomDropdown
              title={<RiMore2Fill size={18} />}
              items={[{ label: "Delete", value: "delete" }]}
              className="table-actions"
              onSelect={onSelect}
              preserveTitle
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="knowledge-base-wrapper">
      <FAQPromptsToolbar setFilters={setFilters} filters={filters} />
      <DataGridTable
        columns={columns}
        rows={prompts}
        paginationProps={{
          paginationConfig,
          setPaginationConfig,
          totalCount,
        }}
        sortDirection={filters.sort}
        onSort={() => {
          setFilters({
            ...filters,
            sort: filters.sort === "asc" ? "desc" : "asc",
          });
        }}
        loading={isLoading}
      />
    </div>
  );
}
