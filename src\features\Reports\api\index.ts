import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api";
import { API_ENDPOINTS } from "./endpoints";

export const useGetReports = (params = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_REPORTS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["reports-list", ...Object.values(params)],
  });

export const useGetReportDetails = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_REPORT_DETAILS(id)
      );
      return response?.data;
    },
    queryKey: ["report-details", id],
    enabled: !!id,
  });

export const useAddReport = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return apiClient.post(API_ENDPOINTS.ADD_REPORT, payload);
    },
  });

export const useDeleteReportMutation = () =>
  useMutation({
    mutationFn: async (id: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_REPORT(id));
    },
  });

export const useEditReport = () =>
  useMutation({
    mutationFn: async ({ id, body }: any) => {
      return apiClient.put(API_ENDPOINTS.EDIT_REPORT(id), body);
    },
  });

export const useUploadReportAsset = () =>
  useMutation({
    mutationFn: async (payload: FormData) => {
      return apiClient.put(API_ENDPOINTS.UPLOAD_REPORT_ASSET, payload);
    },
  });

export const useGetReportAssetMutation = () =>
  useMutation({
    mutationFn: async (assetId: any) => {
      return apiClient.get(API_ENDPOINTS.GET_REPORT_ASSET(assetId));
    },
  });

export const useDeleteReportAsset = () =>
  useMutation({
    mutationFn: async ({ assetId }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_REPORT_ASSET(assetId));
    },
  });

export const useGetSavedSections = (options: any = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_SAVED_SECTIONS);
      return response;
    },
    queryKey: ["saved-sections"],
    ...options,
  });

export const useSaveReportSection = () =>
  useMutation({
    mutationFn: async ({ id, body }: any) => {
      return apiClient.post(API_ENDPOINTS.SAVE_REPORT_SECTION(id), body);
    },
  });

export const useDeleteSavedSection = () =>
  useMutation({
    mutationFn: async ({ id }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_SAVED_SECTION(id));
    },
  });

export const useArchiveReportMutation = () =>
  useMutation({
    mutationFn: async (id: any) => {
      return apiClient.get(API_ENDPOINTS.ARCHIVE_REPORT(id));
    },
  });

export const useArhivedReports = ({ params }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_ARCHIVED_REPORTS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["archived-reports"],
  });

export const useReportCheckCompliance = (body: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.post(
        API_ENDPOINTS.REPORT_CHECK_COMPLIANCE,
        body
      );
      return response;
    },
    queryKey: ["report-check-compliance"],
  });

export const useGetGeneratedReports = (params = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_GENERATED_REPORTS,
        {
          params,
        }
      );
      return response?.data;
    },
    queryKey: ["generated-reports-list", ...Object.values(params)],
  });

export const useGenerateReportMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return apiClient.post(API_ENDPOINTS.GENERATE_REPORT, payload);
    },
  });

export const useGeneratedReportDetails = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_GENERATED_REPORT_DETAILS(id)
      );
      return response?.data;
    },
    queryKey: ["report-details", id],
    enabled: !!id,
  });

export const useSaveGeneratedReportMutation = () =>
  useMutation({
    mutationFn: async ({ id, body }: any) => {
      return apiClient.put(API_ENDPOINTS.SAVE_GENERATED_REPORT(id), body);
    },
  });

export const useDeleteGeneratedReportMutation = () =>
  useMutation({
    mutationFn: async (id: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_GENERATED_REPORT(id));
    },
  });

export const useExportReportMutation = () =>
  useMutation({
    mutationFn: async ({ payload }: { payload: any }) => {
      const response = await apiClient({
        url: API_ENDPOINTS.EXPORT_REPORT,
        method: "POST",
        responseType: "blob",
        data: payload,
      });

      if (!(response instanceof Blob)) {
        console.error("Response is not a Blob:", response);
        throw new Error("Invalid file format received.");
      }

      return response;
    },
  });
