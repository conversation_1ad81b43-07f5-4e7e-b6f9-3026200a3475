import { K<PERSON><PERSON><PERSON>DGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useLocation } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { UserInterface } from "types";

interface IHeaderConfig {
  user: UserInterface;
}

const useHeaderConfig = ({ user }: IHeaderConfig) => {
  const location = useLocation();
  const pathname = location.pathname;

  if (pathname.includes("reports")) {
    return [
      {
        path: REPORTS_ROUTE_PATH.BUILD_REPORTS,
        label: "Build",
        isDefault: true,
      },
      {
        path: REPORTS_ROUTE_PATH.GENERATE_REPORT,
        label: "Generate",
      },
      {
        path: REPORTS_ROUTE_PATH.REPORT_ARCHIVES,
        label: "Archives",
      },
    ];
  }

  if (pathname.includes("knowledge-base") || pathname.includes("faq-prompts")) {
    return [
      {
        path: KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE,
        label: "Knowledge Base",
        isDefault: true,
      },
      {
        path: KNOWLEDGE_BASE_ROUTE_PATH.FAQ_PROMPTS,
        label: "FAQ Prompts",
      },
    ];
  }

  return [
    {
      path: user?.is_subscription ? ROUTE_PATH.HOME : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Home",
      isDefault: true,
    },
  ];
};

export default useHeaderConfig;
